<?php

namespace App\Livewire\Dashboard\Comments;

use App\Models\Comment;
use App\Enums\StatusEnum;
use Livewire\Component;
use LivewireUI\Modal\ModalComponent;

class ViewCommentModal extends ModalComponent
{
    public ?Comment $comment = null;
    public string $commentText = '';

    public $replyText = '';
    public $showReplyForm = false;

    public function mount($commentId)
    {
        $this->comment = Comment::with(['user', 'commentable', 'replies.user'])->find($commentId);

        if (!$this->comment) {
            session()->flash('error', 'کامنت یافت نشد.');
            $this->closeModal();
        }

        $this->commentText = $this->comment->body;
    }

    public function toggleReplyForm()
    {
        $this->showReplyForm = !$this->showReplyForm;
        $this->replyText = '';
    }

    public function submitReply()
    {
        $this->validate([
            'replyText' => 'required|min:3|max:1000'
        ], [
            'replyText.required' => 'متن پاسخ الزامی است.',
            'replyText.min' => 'متن پاسخ باید حداقل 3 کاراکتر باشد.',
            'replyText.max' => 'متن پاسخ نباید بیشتر از 1000 کاراکتر باشد.'
        ]);

        Comment::create([
            'body' => $this->replyText,
            'user_id' => auth()->id(),
            'parent_id' => $this->comment->id,
            'commentable_type' => $this->comment->commentable_type,
            'commentable_id' => $this->comment->commentable_id,
            'status' => StatusEnum::CONFIRMED,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        $this->comment->refresh();
        $this->comment->load(['replies.user']);
        $this->replyText = '';
        $this->showReplyForm = false;

        $this->dispatch('comment-updated');
        session()->flash('message', 'پاسخ شما با موفقیت ثبت شد.');
    }

    public function changeStatus($status)
    {
        $statusEnum = StatusEnum::from((int) $status);
        $this->comment->update(['status' => $statusEnum]);

        $statusText = match ($statusEnum) {
            StatusEnum::PENDING => 'در انتظار تایید',
            StatusEnum::CONFIRMED => 'تایید شده',
            StatusEnum::REJECTED => 'رد شده',
        };

        $this->dispatch('comment-updated');
        session()->flash('message', "وضعیت کامنت به '{$statusText}' تغییر یافت.");
        $this->comment->refresh();
    }

    public function updatedCommentText()
    {
        $this->validate([
            'commentText' => 'required|min:3|max:1000',
        ], [
            'commentText.required' => 'متن کامنت الزامی است.',
            'commentText.min' => 'کامنت باید حداقل ۳ کاراکتر باشد.',
            'commentText.max' => 'کامنت نباید بیشتر از ۱۰۰۰ کاراکتر باشد.',
        ]);

        $this->comment->update(['body' => $this->commentText]);
        $this->dispatch('comment-updated');
        session()->flash('message', 'کامنت با موفقیت ویرایش شد.');
    }

    public function deleteComment()
    {
        $this->comment->delete();
        $this->dispatch('comment-updated');
        session()->flash('message', 'کامنت با موفقیت حذف شد.');
        $this->closeModal();
    }

    public static function modalMaxWidth(): string
    {
        return '4xl';
    }

    public function render()
    {
        return view('livewire.dashboard.comments.view-comment-modal');
    }
}
