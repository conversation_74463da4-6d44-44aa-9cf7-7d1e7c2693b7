<div class="mx-auto w-full max-w-4xl rounded-lg bg-white shadow-xl">
    <!-- Header -->
    <div class="flex items-center justify-between border-b border-gray-200 p-6">
        <h3 class="text-xl font-semibold text-gray-900">جزئیات کامنت</h3>
        <button
            class="text-gray-400 transition-colors hover:text-gray-600"
            wire:click="$dispatch('closeModal')"
        >
            <svg
                class="h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                ></path>
            </svg>
        </button>
    </div>

    <div class="max-h-[90vh] overflow-y-auto p-6">

        @if (!$comment)
            <div class="py-8 text-center">
                <p class="text-gray-500">کامنت یافت نشد.</p>
            </div>
        @else
            <!-- اطلاعات کامنت اصلی -->
            <div class="mb-6 rounded-lg bg-gray-50 p-6">
                <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div class="text-right">
                        <label class="block text-sm font-medium text-gray-700">نام کاربر:</label>
                        <p class="text-sm text-gray-900">{{ $comment->user->fullname ?? 'کاربر ناشناس' }}</p>
                    </div>
                    <div class="text-right">
                        <label class="block text-sm font-medium text-gray-700">شماره تلفن:</label>
                        <p class="text-sm text-gray-900">{{ $comment->user->phone ?? '-' }}</p>
                    </div>
                    <div class="text-right">
                        <label class="block text-sm font-medium text-gray-700">عنوان مقاله:</label>
                        <p class="text-sm text-gray-900">{{ $comment->commentable->title ?? 'مقاله حذف شده' }}</p>
                    </div>
                    <div class="text-right">
                        <label class="block text-sm font-medium text-gray-700">تاریخ ثبت:</label>
                        <p class="text-sm text-gray-900">{{ $comment->shamsi_date }}</p>
                    </div>
                    <div class="text-right">
                        <label class="block text-sm font-medium text-gray-700">امتیاز:</label>
                        <div class="flex items-center">
                            @if ($comment->rate)
                                @for ($i = 1; $i <= 5; $i++)
                                    <svg
                                        class="{{ $i <= $comment->rate ? 'text-yellow-400' : 'text-gray-300' }} h-4 w-4"
                                        fill="currentColor"
                                        viewBox="0 0 20 20"
                                    >
                                        <path
                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                                        />
                                    </svg>
                                @endfor
                                <span class="mr-2 text-sm text-gray-600">({{ $comment->rate }}/5)</span>
                            @else
                                <span class="text-sm text-gray-500">بدون امتیاز</span>
                            @endif
                        </div>
                    </div>
                    <div class="text-right">
                        <label class="block text-sm font-medium text-gray-700">وضعیت:</label>
                        <div class="flex items-center gap-2">
                            @switch($comment->status->value)
                                @case(0)
                                    <span
                                        class="inline-flex rounded-full bg-red-100 px-2 py-1 text-xs font-semibold text-red-800"
                                    >رد
                                        شده</span>
                                @break

                                @case(1)
                                    <span
                                        class="inline-flex rounded-full bg-yellow-100 px-2 py-1 text-xs font-semibold text-yellow-800"
                                    >در انتظار تایید</span>
                                @break

                                @case(2)
                                    <span
                                        class="inline-flex rounded-full bg-green-100 px-2 py-1 text-xs font-semibold text-green-800"
                                    >تایید شده</span>
                                @break
                            @endswitch

                            <!-- تغییر وضعیت -->
                            <select
                                class="rounded border border-gray-300 px-2 py-1 text-xs"
                                wire:change="changeStatus($event.target.value)"
                            >
                                <option
                                    value="{{ $comment->status->value }}"
                                    selected
                                >وضعیت فعلی</option>
                                @if ($comment->status->value !== 1)
                                    <option value="1">در انتظار تایید</option>
                                @endif
                                @if ($comment->status->value !== 2)
                                    <option value="2">تایید</option>
                                @endif
                                @if ($comment->status->value !== 0)
                                    <option value="0">رد</option>
                                @endif
                            </select>
                        </div>
                    </div>
                    <div class="text-right">
                        <label class="block text-sm font-medium text-gray-700">آدرس IP:</label>
                        <p class="font-mono text-sm text-gray-900">{{ $comment->ip_address ?? '-' }}</p>
                    </div>
                    <div class="text-right md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">مرورگر:</label>
                        <p class="truncate text-sm text-gray-900">{{ $comment->user_agent ?? '-' }}</p>
                    </div>
                </div>
                <div class="text-right">
                    <label class="mb-2 block text-sm font-medium text-gray-700">متن نظر:</label>
                    <div class="rounded border bg-white p-3">
                        <textarea
                            class="w-full rounded-lg border-2 border-gray-100 p-2 text-sm leading-relaxed text-gray-900 outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                            rows="8"
                            wire:model.blur="commentText"
                            wire:dirty.class="border-yellow-400"
                        ></textarea>

                    </div>
                </div>
            </div>

            <!-- لیست پاسخ‌ها -->
            @if ($comment->replies && $comment->replies->count() > 0)
                <div class="mb-6 border-t pt-6">
                    <h4 class="text-md mb-4 font-medium text-gray-900">پاسخ‌ها ({{ $comment->replies->count() }})</h4>
                    <div class="space-y-4">
                        @foreach ($comment->replies as $reply)
                            <div class="relative rounded-lg border-r-4 border-blue-200 bg-blue-50 p-4">
                                <div class="mb-2 flex items-center justify-between">
                                    <div class="flex items-center gap-2">
                                        <span class="text-sm font-medium text-gray-900">
                                            {{ $reply->user->fullname ?? 'مدیر سایت' }}
                                        </span>
                                        @if ($reply->user_id === auth()->id())
                                            <span
                                                class="inline-flex rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800"
                                            >
                                                شما
                                            </span>
                                        @endif
                                    </div>
                                    <span class="text-xs text-gray-500">{{ $reply->shamsi_date }}</span>
                                </div>
                                <p class="text-right text-sm leading-relaxed text-gray-700">{{ $reply->body }}</p>
                                <button class=" absolute">

                                </button>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- فرم پاسخ -->
            <div class="border-t pt-6">
                @if (!$showReplyForm)
                    <button
                        class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100"
                        wire:click="toggleReplyForm"
                    >
                        <svg
                            class="ml-2 h-4 w-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"
                            ></path>
                        </svg>
                        پاسخ جدید
                    </button>
                @else
                    <div class="space-y-4">
                        <div>
                            <label
                                class="mb-2 block text-sm font-medium text-gray-700"
                                for="replyText"
                            >
                                متن پاسخ شما:
                            </label>
                            <textarea
                                class="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                id="replyText"
                                wire:model="replyText"
                                rows="4"
                                placeholder="پاسخ خود را اینجا بنویسید..."
                            ></textarea>
                            @error('replyText')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="flex gap-2">
                            <button
                                class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100"
                                wire:click="submitReply"
                            >
                                ارسال پاسخ
                            </button>
                            <button
                                class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100"
                                wire:click="toggleReplyForm"
                            >
                                انصراف
                            </button>
                        </div>
                    </div>
                @endif
            </div>

            <!-- عملیات -->
            <div class="mt-6 border-t pt-6">
                <div class="flex justify-between">
                    <button
                        class="inline-flex items-center rounded-md border border-red-300 bg-red-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-gray-100 hover:text-red-600"
                        wire:click="deleteComment"
                        wire:confirm="آیا از حذف این کامنت اطمینان دارید؟ این عمل قابل بازگشت نیست."
                    >
                        <svg
                            class="ml-2 h-4 w-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                            ></path>
                        </svg>
                        حذف کامنت
                    </button>
                </div>
            </div>
        @endif
    </div>
